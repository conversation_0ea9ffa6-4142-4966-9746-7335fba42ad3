'use client';

import { useState } from 'react';

import { createClient } from 'src/utils/supabase/client';

import { updateData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'users';

/**
 * Cập nhật thông tin người dùng trong cả Supabase Auth và bảng users
 * @param {string} userId - ID của người dùng
 * @param {Object} userData - D<PERSON> liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateUserProfile(userId, userData) {
  if (!userId) {
    return { success: false, error: 'User ID is required', data: null };
  }

  try {
    const supabase = createClient();
    const { displayName, photoURL, phoneNumber, country, address, state, city, zipCode, about, isPublic } = userData;

    // <PERSON><PERSON> lý upload avatar nếu là File object
    let finalAvatarUrl = photoURL;
    if (photoURL instanceof File) {
      const uploadResult = await uploadFileToStorage(photoURL, userId);
      if (!uploadResult.success) {
        return uploadResult;
      }
      finalAvatarUrl = uploadResult.data.publicUrl;
    }

    // Cập nhật thông tin trong Supabase Auth (user_metadata)
    const { data: authData, error: authError } = await supabase.auth.updateUser({
      data: {
        display_name: displayName,
        avatar_url: finalAvatarUrl,
        phone_number: phoneNumber,
        country,
        address,
        state,
        city,
        zip_code: zipCode,
        about,
        is_public: isPublic,
      },
    });

    if (authError) {
      console.error('Error updating auth user:', authError);
      return { success: false, error: authError, data: null };
    }

    // Cập nhật thông tin trong bảng users - chỉ các field tồn tại
    const userRecord = {
      fullName: displayName, // sẽ được convert thành full_name
      phone: phoneNumber,
      avatarUrl: finalAvatarUrl, // sẽ được convert thành avatar_url
      country,
      address,
      state,
      city,
      zipCode, // sẽ được convert thành zip_code
      about,
      isPublic, // sẽ được convert thành is_public
      // Không cập nhật email vì email là thông tin xác thực
    };

    const { data: dbUserData, error: userError } = await updateData(
      TABLE_NAME,
      userRecord,
      { id: userId }
    );

    if (userError) {
      console.error('Error updating user record:', userError);
      return { success: false, error: userError, data: null };
    }

    return {
      success: true,
      data: {
        authUser: authData.user,
        userRecord: dbUserData,
        avatarUrl: finalAvatarUrl,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Upload file lên Supabase Storage
 * @param {File} file - File cần upload
 * @param {string} userId - ID của người dùng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function uploadFileToStorage(file, userId) {
  if (!file || !userId) {
    return { success: false, error: 'File and User ID are required', data: null };
  }

  try {
    const supabase = createClient();
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}-${Date.now()}.${fileExt}`;
    const filePath = `avatars/${fileName}`;

    // Upload file lên Supabase Storage
    const { data, error } = await supabase.storage
      .from('user-files')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
      });

    if (error) {
      console.error('Error uploading file:', error);
      return { success: false, error, data: null };
    }

    // Lấy URL public của file
    const { data: publicUrlData } = supabase.storage
      .from('user-files')
      .getPublicUrl(filePath);

    return {
      success: true,
      data: {
        path: data.path,
        publicUrl: publicUrlData.publicUrl,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in uploadFileToStorage:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật avatar người dùng
 * @param {string} userId - ID của người dùng
 * @param {File} avatarFile - File avatar mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateUserAvatar(userId, avatarFile) {
  if (!userId) {
    return { success: false, error: 'User ID is required', data: null };
  }

  try {
    const supabase = createClient();

    // Upload file lên storage nếu là File object
    let avatarUrl = avatarFile;
    if (avatarFile instanceof File) {
      const uploadResult = await uploadFileToStorage(avatarFile, userId);
      if (!uploadResult.success) {
        return uploadResult;
      }
      avatarUrl = uploadResult.data.publicUrl;
    }

    // Cập nhật avatar trong Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.updateUser({
      data: {
        avatar_url: avatarUrl,
      },
    });

    if (authError) {
      console.error('Error updating auth user avatar:', authError);
      return { success: false, error: authError, data: null };
    }

    // Cập nhật avatar trong bảng users
    const { data: dbUserData, error: userError } = await updateData(
      TABLE_NAME,
      { avatar_url: avatarUrl },
      { id: userId }
    );

    if (userError) {
      console.error('Error updating user avatar in database:', userError);
      return { success: false, error: userError, data: null };
    }

    return {
      success: true,
      data: {
        authUser: authData.user,
        userRecord: dbUserData,
        avatarUrl,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in updateUserAvatar:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật mật khẩu người dùng trong Supabase Auth
 * @param {string} oldPassword - Mật khẩu cũ (không sử dụng trong Supabase, chỉ để validation UI)
 * @param {string} newPassword - Mật khẩu mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateUserPassword(oldPassword, newPassword) {
  try {
    const supabase = createClient();

    // Lấy thông tin user hiện tại
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.email) {
      return { success: false, error: 'Không tìm thấy thông tin người dùng', data: null };
    }

    // Cập nhật mật khẩu mới trực tiếp (Supabase sẽ tự động xác thực session)
    const { data: authData, error: authError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (authError) {
      console.error('Error updating password:', authError);
      return { success: false, error: authError.message, data: null };
    }

    return {
      success: true,
      data: authData,
      error: null,
    };
  } catch (error) {
    console.error('Error in updateUserPassword:', error);
    return { success: false, error: error.message, data: null };
  }
}

/**
 * Hook để quản lý cập nhật thông tin người dùng
 * @returns {Object} - Các hàm và trạng thái
 */
export function useProfileMutations() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isUpdatingAvatar, setIsUpdatingAvatar] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);

  const updateProfile = async (userId, userData) => {
    setIsUpdating(true);
    try {
      return await updateUserProfile(userId, userData);
    } finally {
      setIsUpdating(false);
    }
  };

  const updateAvatar = async (userId, avatarFile) => {
    setIsUpdatingAvatar(true);
    try {
      // Sử dụng updateProfile để upload avatar khi user bấm lưu
      return await updateProfile(userId, { photoURL: avatarFile });
    } finally {
      setIsUpdatingAvatar(false);
    }
  };

  const updatePassword = async (oldPassword, newPassword) => {
    setIsUpdatingPassword(true);
    try {
      return await updateUserPassword(oldPassword, newPassword);
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  return {
    updateProfile,
    updateAvatar,
    updatePassword,
    isUpdating,
    isUpdatingAvatar,
    isUpdatingPassword,
  };
}
