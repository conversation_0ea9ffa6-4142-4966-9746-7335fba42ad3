import { NextResponse } from 'next/server';

import { createClient } from './supabase/server';

// =====================================================
// FULLY OPTIMIZED SERVER AUTH WITH COMPLETE RLS TRUST
// =====================================================
// ✅ MAJOR OPTIMIZATION: Loại bỏ tất cả logic check tenant_id thừa
// Database RLS system handle 100% tenant security:
//
// 1. RLS POLICIES: auth.get_tenant_id() tự động filter data theo user's tenant
//    - SELECT: WHERE tenant_id = auth.get_tenant_id()
//    - INSERT/UPDATE: WITH CHECK tenant_id = auth.get_tenant_id()
//    - DELETE: WHERE tenant_id = auth.get_tenant_id()
//
// 2. AUTO-SET TRIGGERS: auto_set_tenant_id_simple() tự động set tenant_id
//    - Tất cả bảng có BEFORE INSERT trigger
//    - Không cần client gửi tenant_id trong request body
//
// 3. AUTH FUNCTIONS:
//    - auth.get_tenant_id(): Lấy tenant từ JWT token
//    - auth.get_user_tenant_id(): Lấy tenant từ users table
//    - auth.validate_tenant_access(): Validate tenant access
//
// 4. ENHANCED ERROR HANDLING: PostgreSQL error mapping
//
// ✅ RESULT: Server-side code hoàn toàn tin tưởng database-level security
// Không cần validate tenant_id ở client, không cần manual tenant filtering

/**
 * Enhanced PostgreSQL error handler
 * @param {Object} error - PostgreSQL error object
 * @returns {Object} - Formatted error response
 */
function handlePostgreSQLError(error) {
  // RLS Policy violations
  if (error.message?.includes('row-level security') ||
      error.message?.includes('policy') ||
      error.message?.includes('permission denied')) {
    return {
      error: 'Bạn không có quyền truy cập dữ liệu này. Vui lòng kiểm tra lại quyền của bạn.',
      code: 'RLS_POLICY_VIOLATION',
      originalError: error.message,
      status: 403
    };
  }

  // Tenant access violations
  if (error.message?.includes('Invalid tenant_id') ||
      error.message?.includes('Access denied') ||
      error.message?.includes('tenant_id')) {
    return {
      error: 'Bạn không có quyền truy cập tenant này.',
      code: 'TENANT_ACCESS_DENIED',
      originalError: error.message,
      status: 403
    };
  }

  // Foreign key violations
  if (error.code === '23503') {
    return {
      error: 'Dữ liệu tham chiếu không tồn tại hoặc đã bị xóa.',
      code: 'FOREIGN_KEY_VIOLATION',
      originalError: error.message,
      status: 400
    };
  }

  // Unique constraint violations
  if (error.code === '23505') {
    return {
      error: 'Dữ liệu đã tồn tại. Vui lòng kiểm tra lại.',
      code: 'UNIQUE_VIOLATION',
      originalError: error.message,
      status: 409
    };
  }

  // Not null violations
  if (error.code === '23502') {
    return {
      error: 'Thiếu thông tin bắt buộc. Vui lòng kiểm tra lại dữ liệu.',
      code: 'NOT_NULL_VIOLATION',
      originalError: error.message,
      status: 400
    };
  }

  // Check constraint violations
  if (error.code === '23514') {
    return {
      error: 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại giá trị nhập vào.',
      code: 'CHECK_VIOLATION',
      originalError: error.message,
      status: 400
    };
  }

  // Connection errors
  if (error.message?.includes('connection') || error.message?.includes('timeout')) {
    return {
      error: 'Lỗi kết nối cơ sở dữ liệu. Vui lòng thử lại sau.',
      code: 'CONNECTION_ERROR',
      originalError: error.message,
      status: 503
    };
  }

  // Default error
  return {
    error: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
    code: 'UNKNOWN_ERROR',
    originalError: error.message,
    status: 500
  };
}

/**
 * Middleware để xác thực API routes với enhanced error handling
 * @param {Function} handler - Hàm xử lý request
 * @returns {Function} - Hàm middleware đã được bọc
 */
export function withAuth(handler) {
  return async (request, ...args) => {
    try {
      // Tạo Supabase client phía server
      const supabase = await createClient();

      // Kiểm tra xác thực người dùng
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError) {
        console.error('Auth error:', authError);
        const errorResponse = handlePostgreSQLError(authError);
        return NextResponse.json(errorResponse, { status: errorResponse.status });
      }

      if (!user) {
        return NextResponse.json({
          error: 'Unauthorized - Vui lòng đăng nhập để tiếp tục',
          code: 'UNAUTHORIZED'
        }, { status: 401 });
      }

      // Thêm user vào request để handler có thể sử dụng
      request.user = user;
      request.supabase = supabase;

      // Gọi handler gốc
      return handler(request, ...args);
    } catch (error) {
      console.error('Auth middleware error:', error);
      const errorResponse = handlePostgreSQLError(error);
      return NextResponse.json(errorResponse, { status: errorResponse.status });
    }
  };
}

/**
 * ✅ FULLY OPTIMIZED: Middleware với full RLS trust - Loại bỏ tenant validation thừa
 * Database RLS policies + triggers tự động handle tất cả tenant logic:
 * - auth.get_tenant_id() trong RLS policies tự động filter data
 * - auto_set_tenant_id_simple() triggers tự động set tenant_id khi INSERT
 * - Không cần client-side tenant validation
 * @param {Function} handler - Hàm xử lý request
 * @returns {Function} - Hàm middleware đã được bọc
 */
export function withTenantAuth(handler) {
  return async (request, context) => {
    try {
      // Tạo Supabase client phía server
      const supabase = await createClient();

      // Kiểm tra xác thực người dùng
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError) {
        console.error('Auth error:', authError);
        const errorResponse = handlePostgreSQLError(authError);
        return NextResponse.json(errorResponse, { status: errorResponse.status });
      }

      if (!user) {
        return NextResponse.json({
          error: 'Unauthorized - Vui lòng đăng nhập để tiếp tục',
          code: 'UNAUTHORIZED'
        }, { status: 401 });
      }

      // ✅ FULLY OPTIMIZED: Chỉ cần basic user info
      // RLS policies sẽ tự động handle tenant filtering
      // Triggers sẽ tự động set tenant_id khi INSERT
      request.user = user;
      request.supabase = supabase;

      // Gọi handler gốc - Database RLS sẽ handle tất cả tenant logic
      return handler(request, { userId: user.id }, context);
    } catch (error) {
      console.error('Tenant auth middleware error:', error);
      const errorResponse = handlePostgreSQLError(error);
      return NextResponse.json(errorResponse, { status: errorResponse.status });
    }
  };
}

/**
 * ✅ FULLY OPTIMIZED: Simplified request body validation - Trust RLS completely
 * Database RLS policies + triggers handle all tenant logic automatically
 * Chỉ validate JSON format, không cần tenant validation
 * @param {Request} request - Request object
 * @returns {Object} - Kết quả kiểm tra
 */
export async function validateRequestBody(request) {
  try {
    const body = await request.json();

    // Basic JSON structure validation
    if (!body || typeof body !== 'object') {
      return {
        valid: false,
        error: 'Dữ liệu request không hợp lệ. Vui lòng kiểm tra định dạng JSON.',
        code: 'INVALID_JSON_STRUCTURE',
        status: 400,
        body: null,
      };
    }

    // ✅ FULLY OPTIMIZED: Loại bỏ tenant validation
    // Database RLS policies tự động handle tenant access
    // Triggers tự động set tenant_id khi INSERT
    return {
      valid: true,
      error: null,
      status: 200,
      body,
    };

  } catch (error) {
    console.error('Request body validation error:', error);

    // Enhanced JSON parsing error handling
    if (error instanceof SyntaxError) {
      return {
        valid: false,
        error: 'Dữ liệu JSON không hợp lệ. Vui lòng kiểm tra cú pháp.',
        code: 'JSON_SYNTAX_ERROR',
        status: 400,
        body: null,
      };
    }

    return {
      valid: false,
      error: 'Lỗi xử lý request body. Vui lòng thử lại.',
      code: 'REQUEST_PROCESSING_ERROR',
      status: 500,
      body: null,
    };
  }
}

/**
 * @deprecated Use validateRequestBody instead - RLS handles tenant validation
 * Legacy function for backward compatibility
 */
export async function validateTenantId(request, userTenantId = null) {
  console.warn('validateTenantId is deprecated. Use validateRequestBody instead.');
  return validateRequestBody(request);
}

/**
 * ✅ NEW: Enhanced middleware cho admin operations với service role
 * @param {Function} handler - Hàm xử lý request
 * @returns {Function} - Hàm middleware đã được bọc
 */
export function withAdminAuth(handler) {
  return async (request, ...args) => {
    try {
      // Import createAdminClient dynamically để tránh circular dependency
      const { createAdminClient } = await import('./supabase/server');

      // Tạo admin client với service role
      const supabase = createAdminClient();

      // Kiểm tra service role key
      if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
        return NextResponse.json({
          error: 'Service role key not configured',
          code: 'ADMIN_CONFIG_ERROR'
        }, { status: 500 });
      }

      // Thêm admin client vào request
      request.supabase = supabase;
      request.isAdmin = true;

      // Gọi handler gốc
      return handler(request, ...args);
    } catch (error) {
      console.error('Admin auth middleware error:', error);
      const errorResponse = handlePostgreSQLError(error);
      return NextResponse.json(errorResponse, { status: errorResponse.status });
    }
  };
}

/**
 * ✅ FULLY OPTIMIZED: Simplified tenant info - Trust RLS completely
 * RLS policies tự động filter tenant data, không cần manual tenantId check
 * @param {Object} supabase - Supabase client
 * @returns {Promise<Object>} - Current user's tenant information
 */
export async function getCurrentTenantInfo(supabase) {
  try {
    // ✅ FULLY OPTIMIZED: RLS policies tự động filter theo current user's tenant
    // Không cần truyền tenantId, RLS sẽ tự động lấy tenant của user hiện tại
    const { data, error } = await supabase
      .from('tenants')
      .select('id, name, slug, is_active, subscription_plan, subscription_status, max_users, max_stores')
      .single();

    if (error) {
      console.error('Error fetching tenant info:', error);
      return { success: false, error: handlePostgreSQLError(error), data: null };
    }

    return { success: true, error: null, data };
  } catch (error) {
    console.error('Error in getCurrentTenantInfo:', error);
    return { success: false, error: handlePostgreSQLError(error), data: null };
  }
}

/**
 * @deprecated Use getCurrentTenantInfo instead - RLS handles tenant filtering
 * Legacy function for backward compatibility
 */
export async function getTenantInfo(supabase, tenantId) {
  console.warn('getTenantInfo is deprecated. Use getCurrentTenantInfo instead.');
  return getCurrentTenantInfo(supabase);
}
